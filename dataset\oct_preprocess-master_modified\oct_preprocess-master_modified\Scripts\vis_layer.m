load('E:\lh_dataset\hc_flattend\hc01_spectralis_macula_v1_s1_R.mat')

bds = squeeze(data.bds(301:700,20,:))

black = zeros(128, 400, 3);   
imshow(black);
line(1:400, permute(bds, [2 1]), 'LineWidth',3);

color_table = [0,0,0;242,0,0;0,55,35;144,238,255;0,0,255;0,50,0;255,255,0;238,121,66;120,0,0;0,0,0]
black = zeros(128, 400, 3);   
for i = 1:128
    for j = 1:400
        if i < bds(j,1)
            continue
        end
        if i > bds(j,9)
            continue
        end
        
        for k = 1:8
            if i>bds(j,k) && i<bds(j,k+1)
                black(i,j,:) = color_table(k+1,:);
                break
            end
        end
    end
end

imshow(black);