% load('E:\lh_dataset\afterflatten\Farsiu_Ophthalmology_2013_Control_Subject_1017.mat')
% imagesb = mean(data.flat_vol);
% imagesb = squeeze(mean(data.flat_vol));
% bds = data.bds;
% bds(isnan(bds)) = 1;
% bds = int16(bds);
% new_images = zeros([size(imagesb, 1) size(imagesb, 2)]);
% 
% for i = 1:size(new_images, 1)
%     for j = 1:size(new_images, 2)
%         new_images(i, j) = mean(data.flat_vol(:,i,j));%bds(i,j,1) : bds(i,j,3), i, j));
%     end
% end
% 
% mesh(new_images)
% load('E:\lh_dataset\Control 2\Control 2\Farsiu_Ophthalmology_2013_Control_Subject_1017.mat')
% 
% 
% bds = permute(int8(layerMaps), [2 1 3]);
% bds(isnan(bds)) = 1;
% bds(bds==0)=1;
% 
% images = (images - max(images(:))) / (max(images(:)) - min(images(:)));
% tt = squeeze(mean(images));
% 
% new_images = zeros([size(tt, 1) size(tt, 2)]);
% 
% for i = 1:size(new_images, 1)
%     for j = 1:size(new_images, 2)
%         new_images(i, j) = mean(images(bds(i,j,1) : bds(i,j,3), i, j));
%     end
% end
% 
% mesh(tt)
load('E:\lh_dataset\amd_norm_shift\Farsiu_Ophthalmology_2013_AMD_Subject_1157.mat');
bimages = (bimages - min(bimages(:))) / (max(bimages(:)) - min(bimages(:)));
tt = squeeze(mean(bimages));
new_img = zeros(400,410);
for i = 1:41
    new_img(:, i*10-9 : (i+1)*10-10) = repmat(tt(:,i), [1 10]);
end
new_img = (new_img - min(new_img(:))) / (max(new_img(:)) - min(new_img(:)));
imshow(new_img)