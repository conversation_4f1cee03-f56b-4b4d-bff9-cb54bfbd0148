# 支持三种数据集，需要将数据放在指定目录:
- A2A OCT dataset: 从 https://people.duke.edu/~sf59/RPEDC_Ophth_2013_dataset.htm 下载
- JHU OCT dataset: 从 http://iacl.ece.jhu.edu/index.php/Resources 下载
- DME dataset: 从 https://people.duke.edu/~sf59/Chiu_BOE_2014_dataset.htm 下载
## DME数据集
- 数据集下载地址：https://people.duke.edu/~sf59/Chiu_BOE_2014_dataset.htm
- 数据集格式：.mat文件，包含'images', 'automaticFluidDME', 'manualFluid1', 'manualFluid2', 'automaticLayersDME', 'automaticLayersNormal', 'manualLayers1', 'manualLayers2'。
sd_oct_flatten_ori_align_doe函数中使用了'manualLayers1'作为分割结果。
manualLayers1.shape=(8, 512, 61)，意味着给的不是像素值，给的是每一层标注的坐标值，值得注意的是，并不是所有的图片都有标注，里面有空值。![alt text](image.png)
