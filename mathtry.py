import numpy as np
import torch
import matplotlib.pyplot as plt
from dataset.dataset import sd_oct_lesion_test,sd_oct_doe_test
from scipy.io import loadmat, savemat
import torch.nn.functional as F
import scipy.io as sio
import os
from tqdm import tqdm
def test_dataset():
    root = r"C:/Users/<USER>/Desktop/Retinal-OCT-LayerSeg-following-work/Subject_01.mat"  # 包含.mat文件的文件夹路径
    dataset = sd_oct_lesion_test(
        root=root,
        usage="train"
    )
    
    # return patch, torch.tensor(seg).permute(2,0,1), layer_gt, clesion, cidx, nan_mask
    patch, seg, layer_gt,lesion, cidx, nan_mask = dataset[0]
    print("Patch's shape:", patch.shape)       # (1, H, W, D) [1, 224, 48, 41]
    print("Seg's shape:", seg.shape)           # (8, H, W) [8, 48, 11]
    print("Layer_gt's shape:", layer_gt.shape) # (H, W, D) [224, 48, 11]
    print("lesion's shape :", lesion.shape) # [1, 224, 48, 41]
    print("Cidx:", cidx)
    print("Nan_mask shape:", nan_mask.shape) # (H, W)

    # 5. 可视化
    plt.figure(figsize=(15, 5))
    
    # 显示原始图像
    plt.subplot(141)
    plt.imshow(patch[0, :, :, 15].numpy())  # 显示第15个B-scan(其他的都没有lesion)
    plt.title('Original Image')
    
    # 显示分割标签
    plt.subplot(142)
    plt.imshow(seg[:, :, 0].numpy())  # 显示第一个B-scan的分割
    plt.title('Segmentation Label')
    
    # 显示层标签
    plt.subplot(143)
    plt.imshow(layer_gt[:, :, 0].numpy())
    plt.title('Layer Label')
    # 显示lesion
    plt.subplot(144)
    plt.imshow(lesion[0, :, :, 15].numpy())
    plt.title('lesion')
    plt.show()

def test_transformer_shapes():
    # 创建transformer实例
    input_size = (224, 48, 41)
    transformer = model2d_3d.SpatialTransformer2(size=input_size)
    
    # 创建示例输入数据
    batch_size = 2
    xr = torch.randn(batch_size, 1, 224, 48, 41)  # [B, C, H, W, D]
    flows = torch.randn(batch_size, 224, 48, 41)  # [B, H, W, D]
    print("xr (输入图像):", xr.shape)
    flows = flows.unsqueeze(1)  # 添加channel维度 [B, 1, H, W, D]
    print("flows (形变场):", flows.shape)
    print("\n基准网格坐标系形状:", transformer.grid.shape) #[1, 3, 224, 48, 41]
    x_out = transformer(xr, flows)
    new_locs = transformer.grid + flows
    print("\n采样位置形状:", new_locs.shape)
    
    # 归一化坐标
    shape = new_locs.shape[2:]
    for i in range(len(shape)):
        new_locs[:, i, ...] = 2 * (new_locs[:, i, ...] / (shape[i] - 1) - 0.5)
    
    # 调整维度顺序
    new_locs = new_locs.permute(0, 2, 3, 4, 1)
    new_locs = new_locs[..., [2, 1, 0]]
    print("\n归一化并重排后的采样位置形状:", new_locs.shape)
    
    # 执行grid_sample
    output = F.grid_sample(xr, new_locs, 
                          align_corners=True, 
                          mode='bilinear', 
                          padding_mode='border')
    print("\n最终输出形状:", output.shape)
    
    # 测试squeeze操作
    output_squeezed = output.squeeze(1)
    print("\nsqueeze后的输出形状:", output_squeezed.shape)
    
    # 验证形状变化是否符合预期
    print("\n形状变化总结:")
    print(f"输入图像: {xr.shape} -> 输出: {output_squeezed.shape}")
    print(f"形变场: {flows.shape} -> 采样位置: {new_locs.shape}")
    
    return output_squeezed

def process_with_outnan(mat_file_path, output_file_path):
    mat_data = sio.loadmat(mat_file_path)
    oct_images = mat_data['images']  # 形状为(512,1000,100)
    boundaries = mat_data['layerMaps']  #形状为(100,1000,3)
    height, width, num_images = oct_images.shape

    labels = np.zeros_like(oct_images, dtype=np.uint8)
    
    for img_idx in range(num_images):
        # 获取当前图像的边界线数据
        boundary = boundaries[img_idx, :, :]  # 形状为(1000,3)
        valid_mask = ~np.isnan(boundary).any(axis=1)
        valid_indices = np.where(valid_mask)[0]
        if len(valid_indices) == 0:
            continue  # 如果全为NaN，跳过该帧
        
        # 确定裁剪范围
        start_col = valid_indices[0]
        end_col = valid_indices[-1] + 1
        cropped_width = end_col - start_col
        
        # 裁剪图像和边界线
        cropped_image = oct_images[:, start_col:end_col, img_idx]
        cropped_boundaries = boundary[start_col:end_col, :]
        
        # 为裁剪后的图像生成标签
        for col in range(cropped_width):
            # 获取当前列的三条边界线位置
            b1, b2, b3 = cropped_boundaries[col, :]
            
            # 如果边界线有NaN，跳过该列
            if np.isnan([b1, b2, b3]).any():
                continue
            # 将边界线转换为整数（假设边界线值为浮点数）
            b1, b2, b3 = int(b1), int(b2), int(b3)
            # 根据边界线分配标签
            for row in range(height):
                if row < b1:
                    labels[row, col + start_col, img_idx] = 1  # 第一条线以上
                elif b1 <= row < b2:
                    labels[row, col + start_col, img_idx] = 2  # 第一和第二条线之间
                elif b2 <= row < b3:
                    labels[row, col + start_col, img_idx] = 3  # 第二和第三条线之间
                else:
                    labels[row, col + start_col, img_idx] = 4  # 第三条线以下
    
    # 保存标签结果到新的.mat文件
    sio.savemat(output_file_path, {'labels': labels})
    
    return labels

def process_oct_boundaries(mat_file_path, output_file_path):
    mat_data = sio.loadmat(mat_file_path)
    oct_images = mat_data['images']  # 形状为(512,1000,100)
    boundaries = mat_data['layerMaps']  # 形状为(100,1000,3)
    # 获取原始尺寸
    height, orig_width, num_images = oct_images.shape
    
    # 找到全局有效的列范围（对所有图像取交集）
    valid_ranges = []
    for img_idx in range(num_images):
        boundary = boundaries[img_idx, :, :]
        valid_mask = ~np.isnan(boundary).any(axis=1)
        valid_indices = np.where(valid_mask)[0]
        if len(valid_indices) > 0:
            valid_ranges.append((valid_indices[0], valid_indices[-1] + 1))
    if not valid_ranges:
        raise ValueError("所有边界线都为NaN值，无法处理")
    # 确定全局裁剪范围
    start_col = max(start for start, _ in valid_ranges)
    end_col = min(end for _, end in valid_ranges)
    cropped_width = end_col - start_col
    if cropped_width <= 0:
        raise ValueError("有效区域宽度为0，无法处理")
    
    # 初始化裁剪后的图像和标签数组
    cropped_images = np.zeros((height, cropped_width, num_images), dtype=oct_images.dtype)
    labels = np.zeros((height, cropped_width, num_images), dtype=np.uint8)
    
    for img_idx in range(num_images):
        # 裁剪图像
        cropped_images[:, :, img_idx] = oct_images[:, start_col:end_col, img_idx]
        
        # 获取当前图像的边界线数据并裁剪
        cropped_boundaries = boundaries[img_idx, start_col:end_col, :]
        
        # 为裁剪后的图像生成标签
        for col in range(cropped_width):
            # 获取当前列的三条边界线位置
            b1, b2, b3 = cropped_boundaries[col, :]
            
            # 如果边界线有NaN，跳过该列
            if np.isnan([b1, b2, b3]).any():
                continue
                
            # 将边界线转换为整数
            b1, b2, b3 = int(b1), int(b2), int(b3)
            
            # 根据边界线分配标签
            for row in range(height):
                if row < b1:
                    labels[row, col, img_idx] = 1  # 第一条线以上
                elif b1 <= row < b2:
                    labels[row, col, img_idx] = 2  # 第一和第二条线之间
                elif b2 <= row < b3:
                    labels[row, col, img_idx] = 3  # 第二和第三条线之间
                else:
                    labels[row, col, img_idx] = 4  # 第三条线以下
    
    # 保存裁剪后的图像和标签到新的.mat文件
    sio.savemat(output_file_path, {
        'cropped_images': cropped_images,
        'labels': labels
    })
    
    return cropped_images, labels

if __name__ == "__main__":
    input_mat = 'data_tmp\Farsiu_Ophthalmology_2013_Control_Subject_1006.mat'
    output_mat = 'cropped_labels.mat'  # 输出文件路径
    cropped_imgs, result_labels = process_oct_boundaries(input_mat, output_mat)
