% Generate Training Data
% Results:
    % scan: image data [timepoint, Bscan, img_rows, img_cols]
    % bds: manual boundary [timepoint, Bscan, bdc, img_cols]
    %     the label comes from matlab, remenber -1 in boundary points
    % lesion: manual lesion mask [timepoint, Bscan, img_rows, img_cols]
addpath(genpath('../OCTMatTool'));
addpath(genpath('a2a'));
flat_optionsa;
% replace filenames to your own data path

path = "E:\lh_dataset\AMD 2\AMD 2"
path = "E:\lh_dataset\Control 2\Control 2\"
store_path = "E:\lh_dataset\bds_recorded\"
filenames = dir(fullfile(path,'*.mat'));
badfile = []         
% filenames = {    
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_01.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_02.mat' 
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_03.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_04.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_05.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_06.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_07.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_08.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_09.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_10.mat'
%              };
%          

for i = 1:size(filenames, 1)
%     try
        cf = filenames(i, 1)
        cf.name = 'Farsiu_Ophthalmology_2013_AMD_Subject_1073.mat'
        cf.name = 'Farsiu_Ophthalmology_2013_Control_Subject_1055.mat'
        options.segfile = strcat(cf.folder, "\", cf.name);
        filename = strcat(cf.folder, "\", cf.name);
        [data, record_params] = Preprocess(filename,options);

        cstore_path = strcat(store_path, cf.name);
        save(cstore_path, 'data', 'record_params');
        break
%     catch
%         badfile = [badfile,filenames(i,1)]
%         continue
%     end
%     scan = shiftdim(permute(data.flat_vol,[3,1,2]),-1);
%     bds = shiftdim(permute(data.bds,[2,3,1]),-1);
%     % save data
%     % crop to the center 500 pixels as the MICCAI 2017 paper did
%     % val_clims = 135:634;
%     val_clims = 1:768;
%     for j = 1:size(scan,2)
%         image = squeeze(scan(1,j,:,val_clims));
%         label = {};
%         label.bds = squeeze(bds(1,j,:,val_clims));
%         label.lesion = squeeze(lesion(1,j,:,val_clims));
%         label = jsonencode(label);
%         [~,name,~] = fileparts(filename);        
%         fid = fopen(fullfile('label',sprintf('%s_%d.txt',name,j)),'wt');
%         fprintf(fid, label);
%         fclose(fid);        
%         imwrite(image,fullfile('image',sprintf('%s_%d.png',name,j)),'PNG');    
%         fprintf('.')
%     end
    
end


path = "E:\lh_dataset\Control 2\Control 2\"
filenames = dir(fullfile(path,'*.mat'));        
% filenames = {    
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_01.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_02.mat' 
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_03.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_04.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_05.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_06.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_07.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_08.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_09.mat'
%              'E:\lh\layer segmentation\2015_BOE_Chiu\2015_BOE_Chiu\Subject_10.mat'
%              };
%          

% for i = 1:size(filenames, 1)
%     try
%         cf = filenames(i, 1)
%         options.segfile = strcat(cf.folder, "\", cf.name);
%         filename = strcat(cf.folder, "\", cf.name);
%         [data, record_params] = Preprocess(filename,options);
% 
%         cstore_path = strcat(store_path, cf.name);
%         save(cstore_path, 'data', 'record_params');
%     catch
%         badfile = [badfile,filenames(i,1)]
%         continue
%     end
% end

% for i = 1:size(filenames, 1)
%     try
%         cf = filenames(i, 1);
%         options.segfile = strcat(cf.folder, "\", cf.name);
%         filename = strcat(cf.folder, "\", cf.name);
%         [data, record_params] = Preprocess(filename,options);
% 
%         cstore_path = strcat(store_path, cf.name);
%         save(cstore_path, 'data', 'record_params');
%     catch
%         badfile = [badfile,filenames(i,1)]
%         continue
%     end
% %     scan = shiftdim(permute(data.flat_vol,[3,1,2]),-1);
% %     bds = shiftdim(permute(data.bds,[2,3,1]),-1);
% %     % save data
% %     % crop to the center 500 pixels as the MICCAI 2017 paper did
% %     % val_clims = 135:634;
% %     val_clims = 1:768;
% %     for j = 1:size(scan,2)
% %         image = squeeze(scan(1,j,:,val_clims));
% %         label = {};
% %         label.bds = squeeze(bds(1,j,:,val_clims));
% %         label.lesion = squeeze(lesion(1,j,:,val_clims));
% %         label = jsonencode(label);
% %         [~,name,~] = fileparts(filename);        
% %         fid = fopen(fullfile('label',sprintf('%s_%d.txt',name,j)),'wt');
% %         fprintf(fid, label);
% %         fclose(fid);        
% %         imwrite(image,fullfile('image',sprintf('%s_%d.png',name,j)),'PNG');    
% %         fprintf('.')
% %     end
% 
% end